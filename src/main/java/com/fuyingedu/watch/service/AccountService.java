package com.fuyingedu.watch.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fuyingedu.watch.entity.Account;
import com.fuyingedu.watch.entity.BodyRecord;
import com.fuyingedu.watch.mapper.AccountMapper;
import com.fuyingedu.watch.mapper.BodyRecordMapper;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.account.AccountConvertor;
import com.fuyingedu.watch.model.account.InitReq;
import com.fuyingedu.watch.model.account.UpdateBodyRecordReq;
import com.fuyingedu.watch.model.account.UpdateProfileReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@Slf4j
public class AccountService {

    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private BodyRecordMapper bodyRecordMapper;

    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> init(Long uid, InitReq req) {
        // 检查是否已存在该uid的账号
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId).eq(Account::getUid, uid).last("limit 1");
        Account existingAccount = accountMapper.selectOne(queryWrapper);

        if (existingAccount != null) {
            return CommResp.warning("该用户已存在账号");
        }

        // 创建新账号
        Account account = AccountConvertor.toAccount(req);
        account.setUid(uid);
        accountMapper.insert(account);

        // 添加初始身高体重记录
        BodyRecord bodyRecord = new BodyRecord();
        bodyRecord.setAccountId(account.getId());
        bodyRecord.setHeight(req.getHeight());
        bodyRecord.setWeight(req.getWeight());
        bodyRecord.setCreatedAt(LocalDateTime.now());
        bodyRecordMapper.insert(bodyRecord);
        return CommResp.success();
    }

    /**
     * 更新用户资料（昵称、头像、运动宣言）
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> updateProfile(Long uid, UpdateProfileReq req) {
        // 查找用户账号
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId).eq(Account::getUid, uid).eq(Account::getId, req.getId());
        Account account = accountMapper.selectOne(queryWrapper);

        if (account == null) {
            return CommResp.error("用户账号不存在");
        }

        // 更新资料信息
        Account updateAccount = new Account();
        updateAccount.setId(account.getId());
        updateAccount.setUpdatedAt(LocalDateTime.now());

        // 只更新非空字段
        if (req.getNickname() != null) {
            updateAccount.setNickname(req.getNickname());
        }
        if (req.getAvatar() != null) {
            updateAccount.setAvatar(req.getAvatar());
        }
        if (req.getSlogan() != null) {
            updateAccount.setSlogan(req.getSlogan());
        }

        accountMapper.updateById(updateAccount);
        return CommResp.success();
    }

    /**
     * 更新身高体重信息
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> updateBodyRecord(Long uid, UpdateBodyRecordReq req) {
        // 查找用户账号
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId, Account::getHeight, Account::getWeight)
                .eq(Account::getUid, uid).eq(Account::getId, req.getId());
        Account account = accountMapper.selectOne(queryWrapper);

        if (account == null) {
            return CommResp.error("用户账号不存在");
        }

        if (req.getHeight() == null && req.getWeight() == null) {
            return CommResp.error("身高体重信息未变更");
        }

        // 获取当前身高体重值
        Integer currentHeight = account.getHeight();
        Integer currentWeight = account.getWeight();

        // 使用请求中的值，如果为空则使用当前值
        Integer newHeight = req.getHeight() != null ? req.getHeight() : currentHeight;
        Integer newWeight = req.getWeight() != null ? req.getWeight() : currentWeight;

        // 更新账号表中的身高体重
        Account updateAccount = new Account();
        updateAccount.setId(account.getId());
        updateAccount.setHeight(newHeight);
        updateAccount.setWeight(newWeight);
        updateAccount.setUpdatedAt(LocalDateTime.now());
        accountMapper.updateById(updateAccount);

        // 添加身高体重变化记录
        BodyRecord bodyRecord = new BodyRecord();
        bodyRecord.setAccountId(account.getId());
        bodyRecord.setHeight(newHeight);
        bodyRecord.setWeight(newWeight);
        bodyRecord.setCreatedAt(LocalDateTime.now());
        bodyRecordMapper.insert(bodyRecord);

        return CommResp.success();
    }

}
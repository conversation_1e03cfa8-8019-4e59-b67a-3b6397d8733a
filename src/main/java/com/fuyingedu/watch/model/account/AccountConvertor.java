package com.fuyingedu.watch.model.account;

import com.fuyingedu.watch.entity.Account;

public class AccountConvertor {
    public static Account toAccount(InitReq req) {
        Account account = new Account();
        account.setGender(req.getGender() != null ? req.getGender() : (byte) 0);
        account.setBirthDate(req.getBirthDate());
        account.setHeight(req.getHeight());
        account.setWeight(req.getWeight());
        return account;
    }
}

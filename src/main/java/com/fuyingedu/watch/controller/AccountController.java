package com.fuyingedu.watch.controller;

import com.fuyingedu.watch.comm.interceptor.Login;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.account.InitReq;
import com.fuyingedu.watch.model.account.UpdateProfileReq;
import com.fuyingedu.watch.service.AccountService;
import com.fuyingedu.watch.model.account.UpdateBodyRecordReq;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户账号管理
 */
@RestController
@RequestMapping("/api/watch/account")
public class AccountController {

    @Autowired
    private AccountService accountService;

    /**
     * 初始化用户账号
     */
    @Login
    @PostMapping("init")
    public CommResp<?> init(
            @Login Long uid,
            @RequestBody @Valid InitReq req
    ) {
        return accountService.init(uid, req);
    }

    /**
     * 更新用户资料（昵称、头像、运动宣言）
     */
    @Login
    @PostMapping("updateProfile")
    public CommResp<?> updateProfile(
            @Login Long uid,
            @RequestBody @Valid UpdateProfileReq req
    ) {
        return accountService.updateProfile(uid, req);
    }

    /**
     * 更新身高体重信息
     */
    @Login
    @PostMapping("updateBodyRecord")
    public CommResp<?> updateBodyRecord(
            @Login Long uid,
            @RequestBody @Valid UpdateBodyRecordReq req
    ) {
        return accountService.updateBodyRecord(uid, req);
    }

    /**
     * 添加或更新运动目标
     */
    @Login
    @PostMapping("updateExerciseTarget")
    public CommResp<?> saveExerciseTarget(
            @Login Long uid,
            @RequestBody @Valid SaveExerciseTargetReq req
    ) {
        /*
        根据accountId判断是否存在运动目标，存在则更新，不存在则添加
         */
        return accountService.saveExerciseTarget(uid, req);
    }

    /**
     * 根据账号ID获取运动目标
     */
    @Login
    @GetMapping("get")
    public CommResp<?> getAccount(
            @Login Long uid,
            @RequestParam Long accountId
    ) {
        /*
        根据accountId判断用户是否填写了运动目标，如果没有就返回0
         */
        return accountService.getAccount(uid, accountId);
    }
}